import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>ei<PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "DevCrush - Tinder for Developers",
  description: "The ultimate platform for developers to find their perfect coding companions and build amazing projects together. Swipe, match, and collaborate with skilled developers worldwide.",
  keywords: "developers, collaboration, coding, projects, matching, programming, software development",
  authors: [{ name: "DevCrush Team" }],
  openGraph: {
    title: "DevCrush - Tinder for Developers",
    description: "Find your perfect coding companion. Swipe, match, and build amazing projects together.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-black text-white`}
      >
        {children}
      </body>
    </html>
  );
}
