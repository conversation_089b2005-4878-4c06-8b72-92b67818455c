import type { Metadata } from "next";
import { JetBrains_Mono, Inter } from "next/font/google";
import "./globals.css";

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "DevCrush - Tinder for Developers",
  description: "The ultimate platform for developers to find their perfect coding companions and build amazing projects together. Swipe, match, and collaborate with skilled developers worldwide.",
  keywords: "developers, collaboration, coding, projects, matching, programming, software development",
  authors: [{ name: "DevCrush Team" }],
  openGraph: {
    title: "DevCrush - Tinder for Developers",
    description: "Find your perfect coding companion. Swipe, match, and build amazing projects together.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased bg-black text-white font-sans`}
      >
        {children}
      </body>
    </html>
  );
}
