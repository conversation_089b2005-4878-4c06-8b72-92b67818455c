"use client";
import { BentoGrid, BentoGridItem } from "@/components/ui/bento-grid";
import { motion } from "framer-motion";
import {
  IconCode,
  IconUsers,
  IconRocket,
  IconBrandGithub,
  IconMessageCircle,
  IconShield,
  IconTrendingUp,
} from "@tabler/icons-react";

export function FeaturesSection() {
  return (
    <section className="py-20 px-4 bg-black relative">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Everything you need to{" "}
            <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              connect & collaborate
            </span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            DevCrush brings together the best features for developer networking, project collaboration, and skill-based matching.
          </p>
        </motion.div>

        <BentoGrid className="max-w-4xl mx-auto">
          {items.map((item, i) => (
            <BentoGridItem
              key={i}
              title={item.title}
              description={item.description}
              header={item.header}
              icon={item.icon}
              className={i === 3 || i === 6 ? "md:col-span-2" : ""}
            />
          ))}
        </BentoGrid>
      </div>
    </section>
  );
}

const Skeleton = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900"></div>
);

const SkeletonOne = () => {
  const variants = {
    initial: {
      x: 0,
    },
    animate: {
      x: 10,
      rotate: 5,
      transition: {
        duration: 0.2,
      },
    },
  };
  const variantsSecond = {
    initial: {
      x: 0,
    },
    animate: {
      x: -10,
      rotate: -5,
      transition: {
        duration: 0.2,
      },
    },
  };

  return (
    <motion.div
      initial="initial"
      whileHover="animate"
      className="flex flex-1 w-full h-full min-h-[6rem] dark:bg-dot-white/[0.2] bg-dot-black/[0.2] flex-col space-y-2"
    >
      <motion.div
        variants={variants}
        className="flex flex-row rounded-full border border-neutral-100 dark:border-white/[0.2] p-2 items-center space-x-2 bg-white dark:bg-black"
      >
        <div className="h-6 w-6 rounded-full bg-gradient-to-r from-pink-500 to-violet-500 flex-shrink-0" />
        <div className="w-full bg-gray-100 h-4 rounded-full dark:bg-neutral-900" />
      </motion.div>
      <motion.div
        variants={variantsSecond}
        className="flex flex-row rounded-full border border-neutral-100 dark:border-white/[0.2] p-2 items-center space-x-2 w-3/4 ml-auto bg-white dark:bg-black"
      >
        <div className="w-full bg-gray-100 h-4 rounded-full dark:bg-neutral-900" />
        <div className="h-6 w-6 rounded-full bg-gradient-to-r from-pink-500 to-violet-500 flex-shrink-0" />
      </motion.div>
      <motion.div
        variants={variants}
        className="flex flex-row rounded-full border border-neutral-100 dark:border-white/[0.2] p-2 items-center space-x-2 bg-white dark:bg-black"
      >
        <div className="h-6 w-6 rounded-full bg-gradient-to-r from-pink-500 to-violet-500 flex-shrink-0" />
        <div className="w-full bg-gray-100 h-4 rounded-full dark:bg-neutral-900" />
      </motion.div>
    </motion.div>
  );
};

const SkeletonTwo = () => {
  const variants = {
    initial: {
      width: 0,
    },
    animate: {
      width: "100%",
      transition: {
        duration: 0.2,
      },
    },
    hover: {
      width: ["0%", "100%"],
      transition: {
        duration: 2,
      },
    },
  };
  const arr = new Array(6).fill(0);
  return (
    <motion.div
      initial="initial"
      animate="animate"
      whileHover="hover"
      className="flex flex-1 w-full h-full min-h-[6rem] dark:bg-dot-white/[0.2] bg-dot-black/[0.2] flex-col space-y-2"
    >
      {arr.map((_, i) => (
        <motion.div
          key={"skelenton-two" + i}
          variants={variants}
          style={{
            maxWidth: Math.random() * (100 - 40) + 40 + "%",
          }}
          className="flex flex-row rounded-full border border-neutral-100 dark:border-white/[0.2] p-2 items-center space-x-2 bg-neutral-100 dark:bg-neutral-900 w-full h-4"
        ></motion.div>
      ))}
    </motion.div>
  );
};

const items = [
  {
    title: "Smart Matching Algorithm",
    description: "AI-powered matching based on skills, interests, and project compatibility.",
    header: <SkeletonOne />,
    icon: <IconCode className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Real-time Collaboration",
    description: "Built-in tools for seamless project collaboration and communication.",
    header: <SkeletonTwo />,
    icon: <IconUsers className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Project Showcase",
    description: "Display your portfolio and discover exciting projects to join.",
    header: <Skeleton />,
    icon: <IconRocket className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "GitHub Integration",
    description:
      "Seamlessly connect your GitHub profile to showcase your coding skills and contributions.",
    header: <Skeleton />,
    icon: <IconBrandGithub className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Secure Messaging",
    description: "Private, encrypted messaging system for safe communication.",
    header: <Skeleton />,
    icon: <IconMessageCircle className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Reputation System",
    description: "Build trust through peer reviews and project success tracking.",
    header: <Skeleton />,
    icon: <IconShield className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Analytics Dashboard",
    description: "Track your networking progress and collaboration success metrics.",
    header: <Skeleton />,
    icon: <IconTrendingUp className="h-4 w-4 text-neutral-500" />,
  },
];
