"use client";
import { motion } from "framer-motion";
import { IconStar, IconQuote } from "@tabler/icons-react";

export function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Full-Stack Developer",
      company: "TechCorp",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      content: "DevCrush helped me find the perfect co-founder for my SaaS project. The matching algorithm is incredibly accurate!",
      rating: 5,
      tech: ["React", "Node.js", "MongoDB"],
    },
    {
      name: "<PERSON>",
      role: "Mobile Developer",
      company: "StartupXYZ",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      content: "I've launched 3 successful projects through connections made on DevCrush. The community is amazing!",
      rating: 5,
      tech: ["React Native", "Flutter", "Firebase"],
    },
    {
      name: "<PERSON>",
      role: "Backend Engineer",
      company: "DevStudio",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      content: "Finally, a platform that understands developers! The skill-based matching saved me weeks of searching.",
      rating: 5,
      tech: ["Python", "Django", "PostgreSQL"],
    },
    {
      name: "Alex Kim",
      role: "Frontend Developer",
      company: "InnovateLab",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      content: "DevCrush connected me with a designer who perfectly complemented my coding skills. We're building something amazing!",
      rating: 5,
      tech: ["Vue.js", "TypeScript", "Tailwind"],
    },
    {
      name: "Priya Patel",
      role: "DevOps Engineer",
      company: "CloudTech",
      image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
      content: "The security features and verification process make me feel safe networking with other developers.",
      rating: 5,
      tech: ["AWS", "Docker", "Kubernetes"],
    },
    {
      name: "David Thompson",
      role: "AI/ML Engineer",
      company: "DataCorp",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      content: "Found my machine learning project partner in just 2 days. The GitHub integration is a game-changer!",
      rating: 5,
      tech: ["Python", "TensorFlow", "PyTorch"],
    },
  ];

  return (
    <section className="py-20 px-4 bg-gray-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10" />
      
      <div className="max-w-7xl mx-auto relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Loved by{" "}
            <span className="bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
              developers
            </span>{" "}
            worldwide
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Join thousands of developers who have found their perfect coding companions and launched successful projects together.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-black/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-300 group hover:scale-105"
            >
              {/* Quote icon */}
              <div className="flex justify-between items-start mb-4">
                <IconQuote className="w-8 h-8 text-indigo-400 opacity-50" />
                <div className="flex space-x-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <IconStar key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>

              {/* Content */}
              <p className="text-gray-300 mb-6 leading-relaxed">
                "{testimonial.content}"
              </p>

              {/* Tech stack */}
              <div className="flex flex-wrap gap-2 mb-6">
                {testimonial.tech.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="px-2 py-1 bg-gray-800 text-gray-300 text-xs rounded-full border border-gray-700"
                  >
                    {tech}
                  </span>
                ))}
              </div>

              {/* Author */}
              <div className="flex items-center space-x-3">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover border-2 border-gray-700"
                />
                <div>
                  <h4 className="text-white font-semibold">{testimonial.name}</h4>
                  <p className="text-gray-400 text-sm">
                    {testimonial.role} at {testimonial.company}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8 text-center"
        >
          <div>
            <div className="text-3xl font-bold text-indigo-400 mb-2">4.9/5</div>
            <div className="text-gray-400">Average Rating</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-purple-400 mb-2">10K+</div>
            <div className="text-gray-400">Happy Developers</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-pink-400 mb-2">500+</div>
            <div className="text-gray-400">Projects Launched</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-cyan-400 mb-2">95%</div>
            <div className="text-gray-400">Success Rate</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
