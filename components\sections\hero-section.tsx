"use client";
import { Hero<PERSON>igh<PERSON>, Highlight } from "@/components/ui/hero-highlight";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { Button } from "@/components/ui/moving-border";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { motion } from "framer-motion";
import { IconCode, IconRocket } from "@tabler/icons-react";
import Image from "next/image";

export function HeroSection() {
  const words = "Where developers find their perfect coding companion. Swipe, match, and build amazing projects together.";

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <BackgroundBeams className="absolute inset-0" />
      
      <HeroHighlight containerClassName="min-h-screen">
        <motion.div
          initial={{
            opacity: 0,
            y: 20,
          }}
          animate={{
            opacity: 1,
            y: [20, -5, 0],
          }}
          transition={{
            duration: 0.5,
            ease: [0.4, 0.0, 0.2, 1],
          }}
          className="text-center max-w-4xl mx-auto px-4"
        >
          {/* Logo/Brand */}
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="flex items-center justify-center mb-8"
          >
            <div className="flex items-center space-x-3">
              <Image
                src="/logo-flame.png"
                alt="DevCrush Logo"
                width={60}
                height={60}
                className="object-contain"
              />
              <span className="text-4xl font-bold bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent font-mono">
                DevCrush
              </span>
            </div>
          </motion.div>

          {/* Main Headline */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
            className="text-2xl md:text-4xl lg:text-6xl font-bold text-white mb-8 leading-tight"
          >
            Tinder for{" "}
            <Highlight className="text-black dark:text-white">
              Developers
            </Highlight>
          </motion.h1>

          {/* Subtitle with Text Generate Effect */}
          <div className="mb-12">
            <TextGenerateEffect 
              words={words} 
              className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto"
            />
          </div>

          {/* Feature Pills */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <IconCode className="w-4 h-4 text-orange-400" />
              <span className="text-sm text-gray-300">Skill-based Matching</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <IconRocket className="w-4 h-4 text-orange-500" />
              <span className="text-sm text-gray-300">Project Collaboration</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <IconCode className="w-4 h-4 text-red-400" />
              <span className="text-sm text-gray-300">Developer Community</span>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <button className="px-8 py-4 rounded-full bg-gradient-to-r from-orange-500 to-red-600 text-white hover:from-orange-600 hover:to-red-700 transition-all duration-300 text-lg font-semibold min-w-[160px]">
              Start Matching
            </button>

            <button className="px-8 py-4 rounded-full border border-white/30 text-white hover:bg-white/10 transition-all duration-300 text-lg font-medium min-w-[160px]">
              Watch Demo
            </button>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.6 }}
            className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400 font-mono">10K+</div>
              <div className="text-gray-400 text-sm">Active Developers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-500 font-mono">500+</div>
              <div className="text-gray-400 text-sm">Projects Launched</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-400 font-mono">95%</div>
              <div className="text-gray-400 text-sm">Match Success Rate</div>
            </div>
          </motion.div>
        </motion.div>
      </HeroHighlight>
    </div>
  );
}
