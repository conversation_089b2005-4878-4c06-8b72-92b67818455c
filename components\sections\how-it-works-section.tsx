"use client";
import { motion } from "framer-motion";
import { IconUserPlus, IconHeart, IconCode, IconRocket } from "@tabler/icons-react";

export function HowItWorksSection() {
  const steps = [
    {
      icon: <IconUserPlus className="w-8 h-8" />,
      title: "Create Your Profile",
      description: "Set up your developer profile with skills, interests, and project preferences. Connect your GitHub to showcase your work.",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: <IconHeart className="w-8 h-8" />,
      title: "Swipe & Match",
      description: "Browse through developer profiles and projects. Swipe right on those that interest you and get matched when it's mutual.",
      color: "from-pink-500 to-rose-500",
    },
    {
      icon: <IconCode className="w-8 h-8" />,
      title: "Start Collaborating",
      description: "Connect with your matches through secure messaging. Discuss projects, share ideas, and plan your collaboration.",
      color: "from-purple-500 to-indigo-500",
    },
    {
      icon: <IconRocket className="w-8 h-8" />,
      title: "Build Together",
      description: "Launch amazing projects with your new coding companions. Track progress and build lasting professional relationships.",
      color: "from-green-500 to-emerald-500",
    },
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-grid-16" />
      
      <div className="max-w-7xl mx-auto relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            How{" "}
            <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              DevCrush
            </span>{" "}
            Works
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            From profile creation to project launch - your journey to finding the perfect coding companion in four simple steps.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="relative group"
            >
              {/* Connection line */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-gray-600 to-transparent z-0" />
              )}
              
              <div className="relative z-10 bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-300 group-hover:scale-105">
                {/* Step number */}
                <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {index + 1}
                </div>

                {/* Icon */}
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${step.color} flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {step.icon}
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-white mb-3">
                  {step.title}
                </h3>
                <p className="text-gray-400 leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <button className="bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-full font-semibold text-lg hover:from-orange-600 hover:to-red-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
            Get Started Today
          </button>
        </motion.div>
      </div>
    </section>
  );
}
